"use client";var N=Object.create;var R=Object.defineProperty;var V=Object.getOwnPropertyDescriptor;var _=Object.getOwnPropertyNames;var H=Object.getPrototypeOf,W=Object.prototype.hasOwnProperty;var $=(e,s)=>{for(var n in s)R(e,n,{get:s[n],enumerable:!0})},b=(e,s,n,l)=>{if(s&&typeof s=="object"||typeof s=="function")for(let o of _(s))!W.call(e,o)&&o!==n&&R(e,o,{get:()=>s[o],enumerable:!(l=V(s,o))||l.enumerable});return e};var j=(e,s,n)=>(n=e!=null?N(H(e)):{},b(s||!e||!e.__esModule?R(n,"default",{value:e,enumerable:!0}):n,e)),z=e=>b(R({},"__esModule",{value:!0}),e);var ee={};$(ee,{ThemeProvider:()=>F,useTheme:()=>B});module.exports=z(ee);var t=j(require("react"));var I=(e,s,n,l,o,d,u,h)=>{let m=document.documentElement,w=["light","dark"];function p(r){(Array.isArray(e)?e:[e]).forEach(y=>{let k=y==="class",S=k&&d?o.map(f=>d[f]||f):o;k?(m.classList.remove(...S),m.classList.add(d&&d[r]?d[r]:r)):m.setAttribute(y,r)}),C(r)}function C(r){h&&w.includes(r)&&(m.style.colorScheme=r)}function a(){return window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"}if(l)p(l);else try{let r=localStorage.getItem(s)||n,y=u&&r==="system"?a():r;p(y)}catch(r){}};var Q=["light","dark"],D="(prefers-color-scheme: dark)",J=typeof window=="undefined",L=t.createContext(void 0),q={setTheme:e=>{},themes:[]},B=()=>{var e;return(e=t.useContext(L))!=null?e:q},F=e=>t.useContext(L)?t.createElement(t.Fragment,null,e.children):t.createElement(X,{...e}),G=["light","dark"],X=({forcedTheme:e,disableTransitionOnChange:s=!1,enableSystem:n=!0,enableColorScheme:l=!0,storageKey:o="theme",themes:d=G,defaultTheme:u=n?"system":"light",attribute:h="data-theme",value:m,children:w,nonce:p,scriptProps:C})=>{let[a,r]=t.useState(()=>Z(o,u)),[T,y]=t.useState(()=>a==="system"?x():a),k=m?Object.values(m):d,S=t.useCallback(i=>{let c=i;if(!c)return;i==="system"&&n&&(c=x());let v=m?m[c]:c,E=s?K(p):null,P=document.documentElement,M=g=>{g==="class"?(P.classList.remove(...k),v&&P.classList.add(v)):g.startsWith("data-")&&(v?P.setAttribute(g,v):P.removeAttribute(g))};if(Array.isArray(h)?h.forEach(M):M(h),l){let g=Q.includes(u)?u:null,U=Q.includes(c)?c:g;P.style.colorScheme=U}E==null||E()},[p]),f=t.useCallback(i=>{let c=typeof i=="function"?i(a):i;r(c);try{localStorage.setItem(o,c)}catch(v){}},[a]),A=t.useCallback(i=>{let c=x(i);y(c),a==="system"&&n&&!e&&S("system")},[a,e]);t.useEffect(()=>{let i=window.matchMedia(D);return i.addListener(A),A(i),()=>i.removeListener(A)},[A]),t.useEffect(()=>{let i=c=>{c.key===o&&(c.newValue?r(c.newValue):f(u))};return window.addEventListener("storage",i),()=>window.removeEventListener("storage",i)},[f]),t.useEffect(()=>{S(e!=null?e:a)},[e,a]);let O=t.useMemo(()=>({theme:a,setTheme:f,forcedTheme:e,resolvedTheme:a==="system"?T:a,themes:n?[...d,"system"]:d,systemTheme:n?T:void 0}),[a,f,e,T,n,d]);return t.createElement(L.Provider,{value:O},t.createElement(Y,{forcedTheme:e,storageKey:o,attribute:h,enableSystem:n,enableColorScheme:l,defaultTheme:u,value:m,themes:d,nonce:p,scriptProps:C}),w)},Y=t.memo(({forcedTheme:e,storageKey:s,attribute:n,enableSystem:l,enableColorScheme:o,defaultTheme:d,value:u,themes:h,nonce:m,scriptProps:w})=>{let p=JSON.stringify([n,s,d,e,h,u,l,o]).slice(1,-1);return t.createElement("script",{...w,suppressHydrationWarning:!0,nonce:typeof window=="undefined"?m:"",dangerouslySetInnerHTML:{__html:`(${I.toString()})(${p})`}})}),Z=(e,s)=>{if(J)return;let n;try{n=localStorage.getItem(e)||void 0}catch(l){}return n||s},K=e=>{let s=document.createElement("style");return e&&s.setAttribute("nonce",e),s.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(s),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(s)},1)}},x=e=>(e||(e=window.matchMedia(D)),e.matches?"dark":"light");0&&(module.exports={ThemeProvider,useTheme});
