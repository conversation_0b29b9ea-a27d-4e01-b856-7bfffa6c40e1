import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import Navbar from "@/components/Navbar";
import Link from "next/link";

const geistSans = Geist({ variable: "--font-geist-sans", subsets: ["latin"] });
const geistMono = Geist_Mono({ variable: "--font-geist-mono", subsets: ["latin"] });

export const metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen bg-gradient-to-br from-purple-200 to-indigo-100`}
      >
        {/* Header */}
        <header className="sticky top-0 z-10 bg-white/70 backdrop-blur border-b">
          <div className="mx-auto max-w-6xl px-4 h-14 flex items-center">
            <Link href="/" className="flex items-center gap-2 mr-6 shrink-0">
              <span className="text-2xl">🎾</span>
              <span className="font-semibold">Tennista</span>
            </Link>

            <div className="flex-1">
              <Navbar />
            </div>

            <Link
              href="/user"
              className="ml-6 text-sm text-slate-700 hover:text-slate-900 hover:underline shrink-0"
            >
              Welcome, User123
            </Link>
          </div>
        </header>

        {/* Page content */}
        <main className="mx-auto max-w-6xl px-4 py-6">{children}</main>
      </body>
    </html>
  );
}
