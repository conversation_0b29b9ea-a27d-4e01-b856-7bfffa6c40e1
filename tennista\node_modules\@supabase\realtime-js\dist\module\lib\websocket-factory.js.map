{"version": 3, "file": "websocket-factory.js", "sourceRoot": "", "sources": ["../../../src/lib/websocket-factory.ts"], "names": [], "mappings": "AAkCA,MAAM,OAAO,gBAAgB;IACnB,MAAM,CAAC,iBAAiB;;QAC9B,IAAI,OAAO,SAAS,KAAK,WAAW,EAAE,CAAC;YACrC,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,CAAA;QACnD,CAAC;QAED,IACE,OAAO,UAAU,KAAK,WAAW;YACjC,OAAQ,UAAkB,CAAC,SAAS,KAAK,WAAW,EACpD,CAAC;YACD,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAG,UAAkB,CAAC,SAAS,EAAE,CAAA;QACvE,CAAC;QAED,IACE,OAAO,MAAM,KAAK,WAAW;YAC7B,OAAQ,MAAc,CAAC,SAAS,KAAK,WAAW,EAChD,CAAC;YACD,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAG,MAAc,CAAC,SAAS,EAAE,CAAA;QACnE,CAAC;QAED,IACE,OAAO,UAAU,KAAK,WAAW;YACjC,OAAQ,UAAkB,CAAC,aAAa,KAAK,WAAW;YACxD,OAAO,UAAU,CAAC,SAAS,KAAK,WAAW,EAC3C,CAAC;YACD,OAAO;gBACL,IAAI,EAAE,YAAY;gBAClB,KAAK,EACH,yFAAyF;gBAC3F,UAAU,EACR,4GAA4G;aAC/G,CAAA;QACH,CAAC;QAED,IACE,CAAC,OAAO,UAAU,KAAK,WAAW,IAAK,UAAkB,CAAC,WAAW,CAAC;YACtE,CAAC,OAAO,SAAS,KAAK,WAAW;iBAC/B,MAAA,SAAS,CAAC,SAAS,0CAAE,QAAQ,CAAC,aAAa,CAAC,CAAA,CAAC,EAC/C,CAAC;YACD,OAAO;gBACL,IAAI,EAAE,aAAa;gBACnB,KAAK,EACH,mGAAmG;gBACrG,UAAU,EACR,wFAAwF;aAC3F,CAAA;QACH,CAAC;QAED,IACE,OAAO,OAAO,KAAK,WAAW;YAC9B,OAAO,CAAC,QAAQ;YAChB,OAAO,CAAC,QAAQ,CAAC,IAAI,EACrB,CAAC;YACD,MAAM,WAAW,GAAG,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;YAEjE,2CAA2C;YAC3C,IAAI,WAAW,IAAI,EAAE,EAAE,CAAC;gBACtB,oEAAoE;gBACpE,IAAI,OAAO,UAAU,CAAC,SAAS,KAAK,WAAW,EAAE,CAAC;oBAChD,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,UAAU,CAAC,SAAS,EAAE,CAAA;gBAC9D,CAAC;gBACD,6CAA6C;gBAC7C,OAAO;oBACL,IAAI,EAAE,aAAa;oBACnB,KAAK,EAAE,WAAW,WAAW,2CAA2C;oBACxE,UAAU,EACR,8DAA8D;iBACjE,CAAA;YACH,CAAC;YAED,6CAA6C;YAC7C,OAAO;gBACL,IAAI,EAAE,aAAa;gBACnB,KAAK,EAAE,WAAW,WAAW,6CAA6C;gBAC1E,UAAU,EACR,mFAAmF;oBACnF,uBAAuB;oBACvB,4CAA4C;aAC/C,CAAA;QACH,CAAC;QAED,OAAO;YACL,IAAI,EAAE,aAAa;YACnB,KAAK,EAAE,uDAAuD;YAC9D,UAAU,EACR,yHAAyH;SAC5H,CAAA;IACH,CAAC;IAEM,MAAM,CAAC,uBAAuB;QACnC,MAAM,GAAG,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAA;QACpC,IAAI,GAAG,CAAC,WAAW,EAAE,CAAC;YACpB,OAAO,GAAG,CAAC,WAAW,CAAA;QACxB,CAAC;QACD,IAAI,YAAY,GACd,GAAG,CAAC,KAAK,IAAI,8CAA8C,CAAA;QAC7D,IAAI,GAAG,CAAC,UAAU,EAAE,CAAC;YACnB,YAAY,IAAI,2BAA2B,GAAG,CAAC,UAAU,EAAE,CAAA;QAC7D,CAAC;QACD,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAA;IAC/B,CAAC;IAEM,MAAM,CAAC,eAAe,CAC3B,GAAiB,EACjB,SAA6B;QAE7B,MAAM,EAAE,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAA;QACzC,OAAO,IAAI,EAAE,CAAC,GAAG,EAAE,SAAS,CAAC,CAAA;IAC/B,CAAC;IAEM,MAAM,CAAC,oBAAoB;QAChC,IAAI,CAAC;YACH,MAAM,GAAG,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAA;YACpC,OAAO,GAAG,CAAC,IAAI,KAAK,QAAQ,IAAI,GAAG,CAAC,IAAI,KAAK,IAAI,CAAA;QACnD,CAAC;QAAC,WAAM,CAAC;YACP,OAAO,KAAK,CAAA;QACd,CAAC;IACH,CAAC;CACF;AAED,eAAe,gBAAgB,CAAA"}